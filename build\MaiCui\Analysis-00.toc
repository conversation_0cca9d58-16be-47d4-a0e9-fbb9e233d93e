(['C:\\Users\\<USER>\\Desktop\\Mai<PERSON>ui\\Mai<PERSON>ui\\main.py'],
 ['C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui',
  'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui'],
 ['PyQt6.QtCore',
  'PyQt6.QtGui',
  'PyQt6.QtWidgets',
  'requests',
  'json',
  'os',
  'sys',
  'traceback',
  'datetime',
  'threading',
  'urllib.parse',
  'base64',
  'hashlib',
  'time',
  'PyQt6.sip',
  'PyQt6.QtNetwork',
  'PyQt6.QtPrintSupport'],
 [('D:\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 ['tkinter',
  'matplotlib',
  'numpy',
  'scipy',
  'pandas',
  'PIL',
  'cv2',
  'tensorflow',
  'torch',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('config\\GrilmorphismUI.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\GrilmorphismUI.json',
   'DATA'),
  ('config\\app_config.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\app_config.json',
   'DATA'),
  ('config\\auth_storage.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\auth_storage.json',
   'DATA'),
  ('photos\\auto_photo_20250730_183544.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250730_183544.jpg',
   'DATA'),
  ('photos\\photo_20250730_183015.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_183015.jpg',
   'DATA'),
  ('photos\\photo_20250730_184018.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184018.jpg',
   'DATA'),
  ('photos\\photo_20250730_184020.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184020.jpg',
   'DATA'),
  ('photos\\photo_20250730_184215.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184215.jpg',
   'DATA')],
 '3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_pyqt6',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\main.py',
   'PYSOURCE')],
 [('subprocess', 'D:\\python\\Lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\python\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\python\\Lib\\contextlib.py', 'PYMODULE'),
  ('signal', 'D:\\python\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'D:\\python\\Lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'D:\\python\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\python\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\python\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\python\\Lib\\gettext.py', 'PYMODULE'),
  ('_compression', 'D:\\python\\Lib\\_compression.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\python\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\python\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'D:\\python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\python\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'D:\\python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('ipaddress', 'D:\\python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'D:\\python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\python\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\python\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\python\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'D:\\python\\Lib\\getopt.py', 'PYMODULE'),
  ('email.utils', 'D:\\python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('random', 'D:\\python\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\python\\Lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'D:\\python\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\python\\Lib\\numbers.py', 'PYMODULE'),
  ('email.charset', 'D:\\python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('quopri', 'D:\\python\\Lib\\quopri.py', 'PYMODULE'),
  ('email.errors', 'D:\\python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\python\\Lib\\calendar.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib', 'D:\\python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\python\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'D:\\python\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'D:\\python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('string', 'D:\\python\\Lib\\string.py', 'PYMODULE'),
  ('email', 'D:\\python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email._policybase', 'D:\\python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.message', 'D:\\python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header', 'D:\\python\\Lib\\email\\header.py', 'PYMODULE'),
  ('bisect', 'D:\\python\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax', 'D:\\python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'D:\\python\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\python\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('http.client', 'D:\\python\\Lib\\http\\client.py', 'PYMODULE'),
  ('decimal', 'D:\\python\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\python\\Lib\\contextvars.py', 'PYMODULE'),
  ('hmac', 'D:\\python\\Lib\\hmac.py', 'PYMODULE'),
  ('struct', 'D:\\python\\Lib\\struct.py', 'PYMODULE'),
  ('socket', 'D:\\python\\Lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'D:\\python\\Lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'D:\\python\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'D:\\python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'D:\\python\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('py_compile', 'D:\\python\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\python\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('typing', 'D:\\python\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc', 'D:\\python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('inspect', 'D:\\python\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'D:\\python\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'D:\\python\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\python\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\python\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'D:\\python\\Lib\\ast.py', 'PYMODULE'),
  ('__future__', 'D:\\python\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers', 'D:\\python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\python\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib.util', 'D:\\python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'D:\\python\\Lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'D:\\python\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\python\\Lib\\bz2.py', 'PYMODULE'),
  ('logging', 'D:\\python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\python\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\python\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'D:\\python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'D:\\python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\python\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\python\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\python\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\python\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\python\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\python\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\python\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'D:\\python\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'D:\\python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'D:\\python\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'D:\\python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\python\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\python\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'D:\\python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'D:\\python\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'D:\\python\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'D:\\python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('asyncio', 'D:\\python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'D:\\python\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'D:\\python\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\python\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\python\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\python\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'D:\\python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\python\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\python\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\python\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'D:\\python\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\python\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.protocols', 'D:\\python\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.futures', 'D:\\python\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions', 'D:\\python\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines', 'D:\\python\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\python\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'D:\\python\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'D:\\python\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'D:\\python\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'D:\\python\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'D:\\python\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\python\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'D:\\python\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'D:\\python\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\python\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\python\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'D:\\python\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'D:\\python\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'D:\\python\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands', 'D:\\python\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.pager', 'D:\\python\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'D:\\python\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\python\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'D:\\python\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\python\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'D:\\python\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'D:\\python\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', 'D:\\python\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('code', 'D:\\python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\python\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'D:\\python\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\python\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\python\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\python\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'D:\\python\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\python\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\python\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'D:\\python\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'D:\\python\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('_pyrepl.readline', 'D:\\python\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\python\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\python\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'D:\\python\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'D:\\python\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'D:\\python\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\python\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\python\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'D:\\python\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'D:\\python\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'D:\\python\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\python\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\python\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\python\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\python\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\python\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\python\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\python\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\python\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\python\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\python\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\python\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\python\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\python\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\python\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\python\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\python\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\python\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\python\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\python\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\python\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\python\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\python\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\python\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\python\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\python\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\python\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\python\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\python\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'D:\\python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\python\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\python\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\python\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\python\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\python\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\python\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\python\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\python\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\python\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\python\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\python\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\python\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\python\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\python\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\python\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\python\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\python\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\python\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\python\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\python\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\python\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('PyQt6', 'D:\\python\\Lib\\site-packages\\PyQt6\\__init__.py', 'PYMODULE'),
  ('_strptime', 'D:\\python\\Lib\\_strptime.py', 'PYMODULE'),
  ('hashlib', 'D:\\python\\Lib\\hashlib.py', 'PYMODULE'),
  ('base64', 'D:\\python\\Lib\\base64.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('threading', 'D:\\python\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('datetime', 'D:\\python\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('json', 'D:\\python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('requests',
   'D:\\python\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\python\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\python\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\python\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'D:\\python\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests.models',
   'D:\\python\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna', 'D:\\python\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.package_data',
   'D:\\python\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\python\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'D:\\python\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'D:\\python\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\python\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\python\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\python\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\python\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\python\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\python\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\python\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\python\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\python\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\python\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\python\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\python\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\python\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\python\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\python\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\python\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\python\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\python\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\python\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\python\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\python\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\python\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\python\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\python\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\python\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\python\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\python\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\python\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('ui.main_window',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\main_window.py',
   'PYMODULE'),
  ('ui',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__init__.py',
   'PYMODULE'),
  ('api.weight_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\weight_api.py',
   'PYMODULE'),
  ('api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__init__.py',
   'PYMODULE'),
  ('api.auth_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\auth_api.py',
   'PYMODULE'),
  ('api.order_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\order_api.py',
   'PYMODULE'),
  ('api.canteen_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\canteen_api.py',
   'PYMODULE'),
  ('ui.modules.weight_submission_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\weight_submission_module.py',
   'PYMODULE'),
  ('ui.modules',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__init__.py',
   'PYMODULE'),
  ('serial', 'D:\\python\\Lib\\site-packages\\serial\\__init__.py', 'PYMODULE'),
  ('serial.serialjava',
   'D:\\python\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\python\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\python\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\python\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\python\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\python\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('ui.modules.camera_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\camera_module.py',
   'PYMODULE'),
  ('ui.modules.order_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\order_module.py',
   'PYMODULE'),
  ('ui.modules.recipe_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\recipe_module.py',
   'PYMODULE'),
  ('config.settings',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\settings.py',
   'PYMODULE'),
  ('config', '-', 'PYMODULE'),
  ('ui.styles',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\styles.py',
   'PYMODULE'),
  ('utils.auth_manager',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\auth_manager.py',
   'PYMODULE'),
  ('utils',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__init__.py',
   'PYMODULE'),
  ('ui.login_window',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\login_window.py',
   'PYMODULE')],
 [('python313.dll', 'D:\\python\\python313.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd', 'D:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'D:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('PyQt6\\QtPrintSupport.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt6\\QtNetwork.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'BINARY'),
  ('python3.dll', 'D:\\python\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY')],
 [],
 [],
 [('config\\GrilmorphismUI.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\GrilmorphismUI.json',
   'DATA'),
  ('config\\app_config.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\app_config.json',
   'DATA'),
  ('config\\auth_storage.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\auth_storage.json',
   'DATA'),
  ('photos\\auto_photo_20250730_183544.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250730_183544.jpg',
   'DATA'),
  ('photos\\photo_20250730_183015.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_183015.jpg',
   'DATA'),
  ('photos\\photo_20250730_184018.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184018.jpg',
   'DATA'),
  ('photos\\photo_20250730_184020.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184020.jpg',
   'DATA'),
  ('photos\\photo_20250730_184215.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184215.jpg',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\python\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\python\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\base_library.zip',
   'DATA')],
 [('abc', 'D:\\python\\Lib\\abc.py', 'PYMODULE'),
  ('reprlib', 'D:\\python\\Lib\\reprlib.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\python\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec', 'D:\\python\\Lib\\encodings\\uu_codec.py', 'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\python\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'D:\\python\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'D:\\python\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\python\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\python\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'D:\\python\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\python\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\python\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'D:\\python\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\python\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\python\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620', 'D:\\python\\Lib\\encodings\\tis_620.py', 'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\python\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\python\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\python\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'D:\\python\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\python\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\python\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode', 'D:\\python\\Lib\\encodings\\punycode.py', 'PYMODULE'),
  ('encodings.ptcp154', 'D:\\python\\Lib\\encodings\\ptcp154.py', 'PYMODULE'),
  ('encodings.palmos', 'D:\\python\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'D:\\python\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\python\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\python\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\python\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\python\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\python\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\python\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\python\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\python\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\python\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\python\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\python\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1', 'D:\\python\\Lib\\encodings\\latin_1.py', 'PYMODULE'),
  ('encodings.kz1048', 'D:\\python\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'D:\\python\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'D:\\python\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'D:\\python\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'D:\\python\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\python\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\python\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\python\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\python\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\python\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\python\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\python\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\python\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\python\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\python\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\python\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\python\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\python\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\python\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\python\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\python\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\python\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\python\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\python\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\python\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\python\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\python\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\python\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\python\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\python\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\python\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\python\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'D:\\python\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030', 'D:\\python\\Lib\\encodings\\gb18030.py', 'PYMODULE'),
  ('encodings.euc_kr', 'D:\\python\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'D:\\python\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\python\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\python\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'D:\\python\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'D:\\python\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'D:\\python\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'D:\\python\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'D:\\python\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'D:\\python\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'D:\\python\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'D:\\python\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'D:\\python\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'D:\\python\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'D:\\python\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'D:\\python\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'D:\\python\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'D:\\python\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'D:\\python\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'D:\\python\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'D:\\python\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'D:\\python\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'D:\\python\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'D:\\python\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'D:\\python\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'D:\\python\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'D:\\python\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'D:\\python\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'D:\\python\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'D:\\python\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'D:\\python\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'D:\\python\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'D:\\python\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'D:\\python\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'D:\\python\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'D:\\python\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'D:\\python\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'D:\\python\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'D:\\python\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'D:\\python\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'D:\\python\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'D:\\python\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'D:\\python\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'D:\\python\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap', 'D:\\python\\Lib\\encodings\\charmap.py', 'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\python\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\python\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\python\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\python\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'D:\\python\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases', 'D:\\python\\Lib\\encodings\\aliases.py', 'PYMODULE'),
  ('encodings', 'D:\\python\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('operator', 'D:\\python\\Lib\\operator.py', 'PYMODULE'),
  ('functools', 'D:\\python\\Lib\\functools.py', 'PYMODULE'),
  ('io', 'D:\\python\\Lib\\io.py', 'PYMODULE'),
  ('collections', 'D:\\python\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('weakref', 'D:\\python\\Lib\\weakref.py', 'PYMODULE'),
  ('sre_parse', 'D:\\python\\Lib\\sre_parse.py', 'PYMODULE'),
  ('linecache', 'D:\\python\\Lib\\linecache.py', 'PYMODULE'),
  ('sre_constants', 'D:\\python\\Lib\\sre_constants.py', 'PYMODULE'),
  ('codecs', 'D:\\python\\Lib\\codecs.py', 'PYMODULE'),
  ('warnings', 'D:\\python\\Lib\\warnings.py', 'PYMODULE'),
  ('copyreg', 'D:\\python\\Lib\\copyreg.py', 'PYMODULE'),
  ('posixpath', 'D:\\python\\Lib\\posixpath.py', 'PYMODULE'),
  ('heapq', 'D:\\python\\Lib\\heapq.py', 'PYMODULE'),
  ('ntpath', 'D:\\python\\Lib\\ntpath.py', 'PYMODULE'),
  ('sre_compile', 'D:\\python\\Lib\\sre_compile.py', 'PYMODULE'),
  ('_collections_abc', 'D:\\python\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\python\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('types', 'D:\\python\\Lib\\types.py', 'PYMODULE'),
  ('enum', 'D:\\python\\Lib\\enum.py', 'PYMODULE'),
  ('stat', 'D:\\python\\Lib\\stat.py', 'PYMODULE'),
  ('re._parser', 'D:\\python\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\python\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\python\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\python\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'D:\\python\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('locale', 'D:\\python\\Lib\\locale.py', 'PYMODULE'),
  ('keyword', 'D:\\python\\Lib\\keyword.py', 'PYMODULE'),
  ('genericpath', 'D:\\python\\Lib\\genericpath.py', 'PYMODULE'),
  ('traceback', 'D:\\python\\Lib\\traceback.py', 'PYMODULE'),
  ('os', 'D:\\python\\Lib\\os.py', 'PYMODULE')])
