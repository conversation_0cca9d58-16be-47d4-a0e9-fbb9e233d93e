#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品重量提交模块
Product Weight Submission Module
"""

import sys
import re
import json
import os
from typing import Dict, Any, Optional, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QLineEdit, QTextEdit, QMessageBox, QFrame, QScrollArea,
    QGridLayout, QSpacerItem, QSizePolicy, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QMutex
from PyQt6.QtGui import QFont, QPalette, QColor

# 导入摄像头模块
try:
    from .camera_module import CameraModule
except ImportError:
    CameraModule = None
    print("警告: 摄像头模块导入失败，拍照功能将不可用")

from ui.styles import GlassmorphismStyles


class SerialWorker(QThread):
    """串口工作线程"""
    weight_received = pyqtSignal(str)  # 重量数据信号
    error_occurred = pyqtSignal(str)   # 错误信号
    connection_status = pyqtSignal(bool)  # 连接状态信号
    
    def __init__(self, port="COM4", baudrate=9600):
        super().__init__()
        self.port = port
        self.baudrate = baudrate
        self.running = False
        self.serial_connection = None
        self.mutex = QMutex()
        self.data_buffer = ""  # 数据缓冲区
        self.last_valid_weight = None  # 上次有效重量
        self.stable_count = 0  # 稳定计数器
        
    def run(self):
        """运行串口监听"""
        try:
            import serial
            import time
            
            self.serial_connection = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1
            )
            
            self.connection_status.emit(True)
            self.running = True
            
            while self.running:
                try:
                    if self.serial_connection.in_waiting > 0:
                        # 读取所有可用数据
                        raw_data = self.serial_connection.read(self.serial_connection.in_waiting)
                        decoded_data = raw_data.decode('utf-8', errors='ignore')

                        # 添加到缓冲区
                        self.data_buffer += decoded_data

                        # 处理缓冲区中的完整数据包
                        self.process_buffer()

                    time.sleep(0.05)  # 减少延迟，提高响应速度
                    
                except Exception as e:
                    self.error_occurred.emit(f"读取串口数据错误: {str(e)}")
                    
        except ImportError:
            self.error_occurred.emit("未安装pyserial库，请运行: pip install pyserial")
        except Exception as e:
            self.error_occurred.emit(f"串口连接失败: {str(e)}")
            self.connection_status.emit(False)
    
    def process_buffer(self):
        """处理缓冲区数据"""
        # 按换行符分割数据
        lines = self.data_buffer.split('\n')

        # 保留最后一个不完整的行
        self.data_buffer = lines[-1]

        # 处理完整的行
        for line in lines[:-1]:
            line = line.strip()
            if line:
                self.parse_weight_data(line)

    def parse_weight_data(self, data):
        """解析重量数据"""
        # 清理数据：移除可能的重复部分
        cleaned_data = self.clean_serial_data(data)

        if not cleaned_data:
            return

        # 使用正则表达式解析重量数据
        pattern = r'^[sw][gn](-?\d*\.?\d+)kg$'
        match = re.match(pattern, cleaned_data, re.IGNORECASE)

        if match:
            weight_str = match.group(1)
            try:
                weight_float = float(weight_str)
                weight = f"{weight_float:.2f}"

                # 数据稳定性检查
                if self.is_weight_stable(weight_float):
                    self.weight_received.emit(weight)
                    # print(f"📡 串口数据解析成功: '{data}' -> '{cleaned_data}' -> {weight}kg")  # 调试输出已关闭
                else:
                    # print(f"⏳ 重量数据不稳定，等待稳定: {weight}kg")  # 调试输出已关闭
                    pass

            except ValueError:
                # print(f"❌ 重量数据格式错误: {weight_str}")  # 调试输出已关闭
                pass
        else:
            # print(f"❌ 串口数据格式不匹配: '{data}' -> '{cleaned_data}'")  # 调试输出已关闭
            pass

    def clean_serial_data(self, data):
        """清理串口数据"""
        if not data:
            return ""

        # 移除所有空白字符
        data = data.strip()

        # 查找最后一个完整的重量数据模式
        pattern = r'[sw][gn]\d*\.?\d+kg'
        matches = re.findall(pattern, data, re.IGNORECASE)

        if matches:
            # 返回最后一个匹配的完整数据
            return matches[-1]

        return ""

    def is_weight_stable(self, current_weight):
        """检查重量是否稳定"""
        if self.last_valid_weight is None:
            self.last_valid_weight = current_weight
            self.stable_count = 1
            return True

        # 如果重量变化小于0.01kg，认为是稳定的
        if abs(current_weight - self.last_valid_weight) < 0.01:
            self.stable_count += 1
            # 连续3次稳定才发送
            if self.stable_count >= 3:
                return True
        else:
            # 重量变化较大，重置计数器
            self.last_valid_weight = current_weight
            self.stable_count = 1

        return False

    def stop(self):
        """停止串口监听"""
        self.running = False
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.close()
        # 清理缓冲区
        self.data_buffer = ""
        self.last_valid_weight = None
        self.stable_count = 0
        self.connection_status.emit(False)


class WeightSubmissionModule(QWidget):
    """商品重量提交模块"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api = None
        self.current_product = None
        self.current_weights = []  # 存储多个重量值
        self.serial_worker = None
        self.captured_photo_path = None  # 存储拍照路径
        self.current_batch = 0  # 当前批次号
        
        self.init_ui()
        self.setup_connections()
        self.start_serial_monitoring()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)  # 减少边距
        layout.setSpacing(10)  # 减少间距

        # 设置背景样式
        self.setStyleSheet("""
            QWidget {
                background: transparent;
                color: white;
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }
        """)

        # 创建主要内容区域（水平布局）
        main_layout = QHBoxLayout()
        main_layout.setSpacing(10)

        # 左侧区域
        left_layout = QVBoxLayout()
        left_layout.setSpacing(8)

        # 标题（压缩）
        self.create_compact_title(left_layout)

        # 商品信息区域（压缩）
        self.create_compact_product_info_section(left_layout)

        # 重量显示和列表（合并）
        self.create_compact_weight_section(left_layout)

        # 操作按钮区域（压缩）
        self.create_compact_action_buttons(left_layout)

        # 状态栏（压缩）
        self.create_compact_status_bar(left_layout)

        main_layout.addLayout(left_layout, 1)

        # 右侧预留摄像头区域
        self.create_camera_placeholder(main_layout)

        layout.addLayout(main_layout)

    def create_compact_title(self, parent_layout):
        """创建压缩标题"""
        title_label = QLabel("⚖️ 重量提交")
        title_label.setFont(QFont("Inter", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: 600;
                margin: 0px;
                padding: 6px 12px;
                text-align: center;
                background: rgba(147, 51, 234, 0.15);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 8px;
                min-height: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setWordWrap(True)
        title_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        parent_layout.addWidget(title_label)

    def create_title(self, parent_layout):
        """创建标题"""
        title_label = QLabel("⚖️ 商品重量提交")
        title_label.setFont(QFont("Inter", 20, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: 700;
                margin: 0px;
                padding: 10px 0;
                background: transparent;
                border: none;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        parent_layout.addWidget(title_label)

    def create_compact_product_info_section(self, parent_layout):
        """创建压缩商品信息区域"""
        info_card = QFrame()
        info_card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.08);
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 8px;
            }
        """)

        info_layout = QVBoxLayout(info_card)
        info_layout.setSpacing(4)
        info_layout.setContentsMargins(8, 6, 8, 6)

        # 商品信息标题（小字体）
        info_title = QLabel("📦 商品信息")
        info_title.setFont(QFont("Inter", 12, QFont.Weight.Bold))
        info_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: 600;
                margin-bottom: 4px;
                min-height: 18px;
            }
        """)
        info_title.setWordWrap(True)
        info_layout.addWidget(info_title)

        # 商品详细信息（紧凑网格）
        info_grid = QGridLayout()
        info_grid.setSpacing(4)
        info_grid.setContentsMargins(0, 0, 0, 0)

        # 商品信息标签（更小字体）
        self.product_name_label = QLabel("商品: 请选择")
        self.product_code_label = QLabel("编码: -")
        self.order_id_label = QLabel("订单: -")
        self.stock_mode_label = QLabel("类型: -")

        labels = [self.product_name_label, self.product_code_label,
                 self.order_id_label, self.stock_mode_label]

        for i, label in enumerate(labels):
            label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 11px;
                    padding: 4px 6px;
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 4px;
                    min-height: 16px;
                }
            """)
            label.setWordWrap(True)
            label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            info_grid.addWidget(label, i // 2, i % 2)

        info_layout.addLayout(info_grid)
        parent_layout.addWidget(info_card)

    def create_compact_weight_section(self, parent_layout):
        """创建压缩重量区域（合并显示和列表）"""
        weight_card = QFrame()
        weight_card.setStyleSheet("""
            QFrame {
                background: rgba(59, 130, 246, 0.08);
                border: 1px solid rgba(59, 130, 246, 0.2);
                border-radius: 8px;
                padding: 8px;
            }
        """)

        weight_layout = QVBoxLayout(weight_card)
        weight_layout.setSpacing(6)
        weight_layout.setContentsMargins(8, 6, 8, 6)

        # 重量显示区域（水平布局）
        weight_display_layout = QHBoxLayout()
        weight_display_layout.setSpacing(8)

        # 当前重量显示（左侧）
        weight_info_layout = QVBoxLayout()
        weight_info_layout.setSpacing(2)

        weight_title = QLabel("⚖️ 当前重量")
        weight_title.setFont(QFont("Inter", 12, QFont.Weight.Bold))
        weight_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: 600;
                min-height: 18px;
            }
        """)
        weight_title.setWordWrap(True)
        weight_info_layout.addWidget(weight_title)

        self.current_weight_label = QLabel("0.00 kg")
        self.current_weight_label.setFont(QFont("Inter", 20, QFont.Weight.Bold))
        self.current_weight_label.setStyleSheet("""
            QLabel {
                color: rgba(59, 130, 246, 1);
                font-size: 20px;
                font-weight: 700;
                text-align: center;
                padding: 8px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                min-height: 40px;
            }
        """)
        self.current_weight_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_weight_label.setWordWrap(True)
        weight_info_layout.addWidget(self.current_weight_label)

        # 串口状态
        self.serial_status_label = QLabel("🔴 串口未连接")
        self.serial_status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 10px;
                text-align: center;
                padding: 2px;
                min-height: 14px;
            }
        """)
        self.serial_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.serial_status_label.setWordWrap(True)
        weight_info_layout.addWidget(self.serial_status_label)

        weight_display_layout.addLayout(weight_info_layout, 1)

        # 重量列表（右侧，压缩）
        list_layout = QVBoxLayout()
        list_layout.setSpacing(2)

        list_title = QLabel("📋 记录")
        list_title.setFont(QFont("Inter", 12, QFont.Weight.Bold))
        list_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: 600;
                min-height: 18px;
            }
        """)
        list_title.setWordWrap(True)
        list_layout.addWidget(list_title)

        # 重量列表显示区域（更小）
        self.weight_list_area = QScrollArea()
        self.weight_list_area.setWidgetResizable(True)
        self.weight_list_area.setMaximumHeight(80)  # 减小高度
        self.weight_list_area.setStyleSheet("""
            QScrollArea {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 6px;
                border-radius: 3px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                min-height: 15px;
            }
        """)

        self.weight_list_widget = QWidget()
        self.weight_list_layout = QVBoxLayout(self.weight_list_widget)
        self.weight_list_layout.setSpacing(2)
        self.weight_list_layout.setContentsMargins(4, 2, 4, 2)

        # 默认提示
        empty_label = QLabel("暂无重量记录")
        empty_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.5);
                font-size: 10px;
                text-align: center;
                padding: 8px;
                min-height: 16px;
            }
        """)
        empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_label.setWordWrap(True)
        self.weight_list_layout.addWidget(empty_label)

        self.weight_list_area.setWidget(self.weight_list_widget)
        list_layout.addWidget(self.weight_list_area)

        weight_display_layout.addLayout(list_layout, 1)
        weight_layout.addLayout(weight_display_layout)

        parent_layout.addWidget(weight_card)

    def create_compact_action_buttons(self, parent_layout):
        """创建压缩操作按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        # 添加当前重量按钮（压缩）
        self.add_weight_btn = QPushButton("➕ 添加")
        self.add_weight_btn.setFont(QFont("Inter", 10, QFont.Weight.Medium))
        self.add_weight_btn.setStyleSheet("""
            QPushButton {
                background: rgba(34, 197, 94, 0.2);
                border: 1px solid rgba(34, 197, 94, 0.3);
                border-radius: 6px;
                padding: 6px 12px;
                color: white;
                font-weight: 500;
                font-size: 10px;
                min-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover:enabled {
                background: rgba(34, 197, 94, 0.3);
            }
            QPushButton:pressed {
                background: rgba(34, 197, 94, 0.4);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.add_weight_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button_layout.addWidget(self.add_weight_btn)

        # 清空重量列表按钮（压缩）
        self.clear_weights_btn = QPushButton("🗑️ 清空")
        self.clear_weights_btn.setFont(QFont("Inter", 10, QFont.Weight.Medium))
        self.clear_weights_btn.setStyleSheet("""
            QPushButton {
                background: rgba(239, 68, 68, 0.2);
                border: 1px solid rgba(239, 68, 68, 0.3);
                border-radius: 6px;
                padding: 6px 12px;
                color: white;
                font-weight: 500;
                font-size: 10px;
                min-height: 28px;
                min-width: 50px;
            }
            QPushButton:hover:enabled {
                background: rgba(239, 68, 68, 0.3);
            }
            QPushButton:pressed {
                background: rgba(239, 68, 68, 0.4);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.clear_weights_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button_layout.addWidget(self.clear_weights_btn)

        # 提交重量按钮（压缩）
        self.submit_btn = QPushButton("📤 提交")
        self.submit_btn.setFont(QFont("Inter", 11, QFont.Weight.Bold))
        self.submit_btn.setStyleSheet("""
            QPushButton {
                background: rgba(147, 51, 234, 0.2);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 6px;
                padding: 8px 16px;
                color: white;
                font-weight: 600;
                font-size: 11px;
                min-height: 32px;
                min-width: 70px;
            }
            QPushButton:hover:enabled {
                background: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 12px rgba(147, 51, 234, 0.2);
            }
            QPushButton:pressed {
                background: rgba(147, 51, 234, 0.4);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.submit_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button_layout.addWidget(self.submit_btn)

        parent_layout.addLayout(button_layout)

    def create_compact_status_bar(self, parent_layout):
        """创建压缩状态栏"""
        status_layout = QHBoxLayout()
        status_layout.setSpacing(8)
        status_layout.setContentsMargins(0, 0, 0, 0)

        # 状态标签
        self.status_label = QLabel("✅ 准备就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 10px;
                padding: 4px 8px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 4px;
                min-height: 16px;
            }
        """)
        self.status_label.setWordWrap(True)
        self.status_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        status_layout.addWidget(self.status_label)

        # 进度条（压缩）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(12)  # 更小的进度条
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                height: 12px;
                text-align: center;
                color: white;
                font-size: 9px;
            }
            QProgressBar::chunk {
                background: rgba(147, 51, 234, 0.8);
                border-radius: 5px;
            }
        """)
        status_layout.addWidget(self.progress_bar)

        parent_layout.addLayout(status_layout)

    def create_camera_placeholder(self, parent_layout):
        """创建摄像头区域"""
        camera_card = QFrame()
        camera_card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 6px;
            }
        """)
        camera_card.setMinimumWidth(200)
        camera_card.setMaximumWidth(250)

        camera_layout = QVBoxLayout(camera_card)
        camera_layout.setContentsMargins(4, 4, 4, 4)
        camera_layout.setSpacing(4)

        # 摄像头标题
        camera_title = QLabel("📷 摄像头")
        camera_title.setFont(QFont("Inter", 12, QFont.Weight.Bold))
        camera_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: 600;
                text-align: center;
                padding: 4px;
                min-height: 18px;
            }
        """)
        camera_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        camera_title.setWordWrap(True)
        camera_layout.addWidget(camera_title)

        # 创建摄像头模块
        if CameraModule:
            self.camera_module = CameraModule()
            self.camera_module.photo_captured.connect(self.on_photo_captured)
            camera_layout.addWidget(self.camera_module)
        else:
            # 摄像头不可用时的占位符
            placeholder = QLabel("📷\n摄像头不可用\n请安装opencv-python")
            placeholder.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.5);
                    font-size: 10px;
                    text-align: center;
                    padding: 20px;
                    min-height: 100px;
                }
            """)
            placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder.setWordWrap(True)
            camera_layout.addWidget(placeholder)
            self.camera_module = None

        parent_layout.addWidget(camera_card)

    def create_product_info_section(self, parent_layout):
        """创建商品信息区域"""
        # 商品信息卡片
        info_card = QFrame()
        info_card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
                padding: 20px;
            }
        """)
        
        info_layout = QVBoxLayout(info_card)
        info_layout.setSpacing(15)
        
        # 商品信息标题
        info_title = QLabel("📦 商品信息")
        info_title.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        info_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 10px;
            }
        """)
        info_layout.addWidget(info_title)
        
        # 商品详细信息网格
        info_grid = QGridLayout()
        info_grid.setSpacing(10)
        
        # 商品名称
        self.product_name_label = QLabel("商品名称: 请选择商品")
        self.product_code_label = QLabel("商品编码: -")
        self.order_id_label = QLabel("订单ID: -")
        self.stock_mode_label = QLabel("入库类型: -")
        
        labels = [
            self.product_name_label,
            self.product_code_label, 
            self.order_id_label,
            self.stock_mode_label
        ]
        
        for i, label in enumerate(labels):
            label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    padding: 8px;
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 8px;
                    min-height: 40px;
                }
            """)
            # 设置文字换行和尺寸策略
            label.setWordWrap(True)
            label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
            info_grid.addWidget(label, i // 2, i % 2)
        
        info_layout.addLayout(info_grid)
        parent_layout.addWidget(info_card)
    
    def create_weight_display_section(self, parent_layout):
        """创建重量显示区域"""
        weight_card = QFrame()
        weight_card.setStyleSheet("""
            QFrame {
                background: rgba(59, 130, 246, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 16px;
                padding: 20px;
            }
        """)
        
        weight_layout = QVBoxLayout(weight_card)
        weight_layout.setSpacing(15)
        
        # 当前重量标题
        weight_title = QLabel("⚖️ 当前重量")
        weight_title.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        weight_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 10px;
                min-height: 30px;
            }
        """)
        weight_title.setWordWrap(True)
        weight_title.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        weight_layout.addWidget(weight_title)
        
        # 重量显示
        self.current_weight_label = QLabel("0.00 kg")
        self.current_weight_label.setFont(QFont("Inter", 32, QFont.Weight.Bold))
        self.current_weight_label.setStyleSheet("""
            QLabel {
                color: rgba(59, 130, 246, 1);
                font-size: 32px;
                font-weight: 700;
                text-align: center;
                padding: 20px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                min-height: 80px;
            }
        """)
        self.current_weight_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_weight_label.setWordWrap(True)
        self.current_weight_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        weight_layout.addWidget(self.current_weight_label)
        
        # 串口状态
        self.serial_status_label = QLabel("🔴 串口未连接")
        self.serial_status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
                text-align: center;
                padding: 5px;
                min-height: 25px;
            }
        """)
        self.serial_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.serial_status_label.setWordWrap(True)
        self.serial_status_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        weight_layout.addWidget(self.serial_status_label)
        
        parent_layout.addWidget(weight_card)

    def create_weight_list_section(self, parent_layout):
        """创建重量列表区域"""
        list_card = QFrame()
        list_card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
                padding: 20px;
            }
        """)

        list_layout = QVBoxLayout(list_card)
        list_layout.setSpacing(15)

        # 重量列表标题
        list_title = QLabel("📋 重量记录")
        list_title.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        list_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 10px;
                min-height: 30px;
            }
        """)
        list_title.setWordWrap(True)
        list_title.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        list_layout.addWidget(list_title)

        # 重量列表显示区域
        self.weight_list_area = QScrollArea()
        self.weight_list_area.setWidgetResizable(True)
        self.weight_list_area.setMaximumHeight(150)
        self.weight_list_area.setStyleSheet("""
            QScrollArea {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
        """)

        self.weight_list_widget = QWidget()
        self.weight_list_layout = QVBoxLayout(self.weight_list_widget)
        self.weight_list_layout.setSpacing(5)

        # 默认提示
        self.no_weights_label = QLabel("暂无重量记录")
        self.no_weights_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.5);
                font-size: 14px;
                text-align: center;
                padding: 20px;
            }
        """)
        self.no_weights_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.weight_list_layout.addWidget(self.no_weights_label)

        self.weight_list_area.setWidget(self.weight_list_widget)
        list_layout.addWidget(self.weight_list_area)

        parent_layout.addWidget(list_card)

    def create_action_buttons(self, parent_layout):
        """创建操作按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 添加当前重量按钮
        self.add_weight_btn = QPushButton("➕ 添加当前重量")
        self.add_weight_btn.setFont(QFont("Inter", 12, QFont.Weight.Medium))
        self.add_weight_btn.setStyleSheet("""
            QPushButton {
                background: rgba(34, 197, 94, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(34, 197, 94, 0.3);
                border-radius: 12px;
                padding: 12px 24px;
                color: white;
                font-weight: 500;
                font-size: 12px;
                min-height: 45px;
                min-width: 120px;
            }
            QPushButton:hover:enabled {
                background: rgba(34, 197, 94, 0.3);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: rgba(34, 197, 94, 0.4);
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.add_weight_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button_layout.addWidget(self.add_weight_btn)

        # 清空重量列表按钮
        self.clear_weights_btn = QPushButton("🗑️ 清空列表")
        self.clear_weights_btn.setFont(QFont("Inter", 12, QFont.Weight.Medium))
        self.clear_weights_btn.setStyleSheet("""
            QPushButton {
                background: rgba(239, 68, 68, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(239, 68, 68, 0.3);
                border-radius: 12px;
                padding: 12px 24px;
                color: white;
                font-weight: 500;
                font-size: 12px;
                min-height: 45px;
                min-width: 100px;
            }
            QPushButton:hover:enabled {
                background: rgba(239, 68, 68, 0.3);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: rgba(239, 68, 68, 0.4);
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.clear_weights_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button_layout.addWidget(self.clear_weights_btn)

        # 提交重量按钮
        self.submit_btn = QPushButton("📤 提交重量")
        self.submit_btn.setFont(QFont("Inter", 14, QFont.Weight.Bold))
        self.submit_btn.setStyleSheet("""
            QPushButton {
                background: rgba(147, 51, 234, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 12px;
                padding: 15px 30px;
                color: white;
                font-weight: 600;
                font-size: 14px;
                min-height: 50px;
                min-width: 140px;
            }
            QPushButton:hover:enabled {
                background: rgba(147, 51, 234, 0.3);
                box-shadow: 0 12px 40px rgba(147, 51, 234, 0.2);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: rgba(147, 51, 234, 0.4);
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.submit_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        button_layout.addWidget(self.submit_btn)

        parent_layout.addLayout(button_layout)

    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_layout = QHBoxLayout()

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
                padding: 5px;
            }
        """)
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                height: 16px;
                text-align: center;
                color: white;
            }
            QProgressBar::chunk {
                background: rgba(147, 51, 234, 0.8);
                border-radius: 8px;
            }
        """)
        status_layout.addWidget(self.progress_bar)

        parent_layout.addLayout(status_layout)

    def setup_connections(self):
        """设置信号连接"""
        self.add_weight_btn.clicked.connect(self.add_current_weight)
        self.clear_weights_btn.clicked.connect(self.clear_weight_list)
        self.submit_btn.clicked.connect(self.submit_weights)

        # 测试串口数据解析（调试用）
        self.test_serial_parsing()

    def start_serial_monitoring(self):
        """启动串口监听"""
        try:
            self.serial_worker = SerialWorker("COM4", 9600)
            self.serial_worker.weight_received.connect(self.on_weight_received)
            self.serial_worker.error_occurred.connect(self.on_serial_error)
            self.serial_worker.connection_status.connect(self.on_connection_status_changed)
            self.serial_worker.start()

            self.status_label.setText("正在连接串口...")

        except Exception as e:
            self.on_serial_error(f"启动串口监听失败: {str(e)}")

    def stop_serial_monitoring(self):
        """停止串口监听"""
        if self.serial_worker:
            self.serial_worker.stop()
            self.serial_worker.wait()
            self.serial_worker = None

    def set_product_info(self, product_data: Dict[str, Any]):
        """设置商品信息"""
        self.current_product = product_data

        # 更新商品信息显示
        name = product_data.get('name', '未知商品')
        code = product_data.get('code', '-')
        product_id = product_data.get('id', '-')  # 这是商品ID，用于API提交
        stock_mode = self.get_stock_mode_text(product_data.get('stock_mode', 0))

        self.product_name_label.setText(f"商品名称: {name}")
        self.product_code_label.setText(f"商品编码: {code}")
        self.order_id_label.setText(f"商品ID: {product_id}")  # 修正显示标签
        self.stock_mode_label.setText(f"入库类型: {stock_mode}")

        # 清空之前的重量记录
        self.clear_weight_list()

        self.status_label.setText(f"已加载商品: {name}")

    def get_stock_mode_text(self, stock_mode: int) -> str:
        """获取入库类型文本"""
        mode_map = {
            0: "即入即出",
            1: "先入后出"
        }
        return mode_map.get(stock_mode, "未知")

    def on_weight_received(self, weight_str: str):
        """接收到重量数据"""
        try:
            weight = float(weight_str)
            self.current_weight_label.setText(f"{weight:.2f} kg")

            # 更新状态
            self.status_label.setText(f"接收到重量: {weight:.2f} kg")

        except ValueError:
            self.on_serial_error(f"无效的重量数据: {weight_str}")

    def on_serial_error(self, error_message: str):
        """处理串口错误"""
        self.status_label.setText(f"串口错误: {error_message}")
        self.serial_status_label.setText("🔴 串口连接异常")
        self.serial_status_label.setStyleSheet("""
            QLabel {
                color: rgba(239, 68, 68, 0.8);
                font-size: 12px;
                text-align: center;
                padding: 5px;
            }
        """)

    def on_connection_status_changed(self, connected: bool):
        """连接状态变化"""
        if connected:
            self.serial_status_label.setText("🟢 串口已连接 (COM4)")
            self.serial_status_label.setStyleSheet("""
                QLabel {
                    color: rgba(34, 197, 94, 0.8);
                    font-size: 12px;
                    text-align: center;
                    padding: 5px;
                }
            """)
            self.status_label.setText("串口连接成功，等待重量数据...")
        else:
            self.serial_status_label.setText("🔴 串口未连接")
            self.serial_status_label.setStyleSheet("""
                QLabel {
                    color: rgba(239, 68, 68, 0.8);
                    font-size: 12px;
                    text-align: center;
                    padding: 5px;
                }
            """)

    def add_current_weight(self):
        """添加当前重量到列表"""
        current_text = self.current_weight_label.text()
        if current_text == "0.00 kg":
            QMessageBox.warning(self, "警告", "当前没有有效的重量数据")
            return

        try:
            # 提取重量数值
            weight_str = current_text.replace(" kg", "")
            weight = float(weight_str)

            if weight <= 0:
                QMessageBox.warning(self, "警告", "重量必须大于0")
                return

            # 添加到重量列表
            self.current_weights.append(weight)
            self.update_weight_list_display()

            self.status_label.setText(f"已添加重量: {weight:.2f} kg (共{len(self.current_weights)}个)")

        except ValueError:
            QMessageBox.warning(self, "警告", "无效的重量数据")

    def clear_weight_list(self):
        """清空重量列表"""
        self.current_weights.clear()
        self.update_weight_list_display()
        self.status_label.setText("已清空重量列表")

    def update_weight_list_display(self):
        """更新重量列表显示"""
        # 清空现有显示
        while self.weight_list_layout.count():
            child = self.weight_list_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.current_weights:
            # 显示空状态（压缩版）
            self.no_weights_label = QLabel("暂无记录")
            self.no_weights_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.5);
                    font-size: 10px;
                    text-align: center;
                    padding: 8px;
                    min-height: 16px;
                }
            """)
            self.no_weights_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.no_weights_label.setWordWrap(True)
            self.weight_list_layout.addWidget(self.no_weights_label)
        else:
            # 显示重量列表
            for i, weight in enumerate(self.current_weights):
                weight_item = self.create_weight_item(i + 1, weight)
                self.weight_list_layout.addWidget(weight_item)

        # 更新按钮状态
        self.update_button_states()

    def create_weight_item(self, index: int, weight: float) -> QWidget:
        """创建重量项目（压缩版）"""
        item_widget = QFrame()
        item_widget.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.08);
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 4px;
                padding: 2px;
                margin: 1px;
            }
        """)

        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(4, 2, 4, 2)
        item_layout.setSpacing(4)

        # 序号和重量（更小字体）
        weight_label = QLabel(f"{index}. {weight:.2f}kg")
        weight_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 9px;
                font-weight: 500;
                min-height: 12px;
            }
        """)
        weight_label.setWordWrap(True)
        weight_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        item_layout.addWidget(weight_label)

        # 删除按钮（更小）
        delete_btn = QPushButton("×")
        delete_btn.setFixedSize(16, 16)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: rgba(239, 68, 68, 0.2);
                border: 1px solid rgba(239, 68, 68, 0.3);
                border-radius: 8px;
                color: white;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 0.4);
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_weight_item(index - 1))
        item_layout.addWidget(delete_btn)

        return item_widget

    def remove_weight_item(self, index: int):
        """删除重量项目"""
        if 0 <= index < len(self.current_weights):
            removed_weight = self.current_weights.pop(index)
            self.update_weight_list_display()
            self.status_label.setText(f"已删除重量: {removed_weight:.2f} kg")

    def update_button_states(self):
        """更新按钮状态"""
        has_weights = len(self.current_weights) > 0
        has_product = self.current_product is not None

        self.clear_weights_btn.setEnabled(has_weights)
        self.submit_btn.setEnabled(has_weights and has_product)

    def submit_weights(self):
        """提交重量数据"""
        if not self.current_product:
            QMessageBox.warning(self, "警告", "请先选择商品")
            return

        if not self.current_weights:
            QMessageBox.warning(self, "警告", "请先添加重量数据")
            return

        if not self.api:
            QMessageBox.warning(self, "警告", "API未初始化")
            return

        # 确认提交
        weights_str = ", ".join([f"{w:.2f}" for w in self.current_weights])
        product_name = self.current_product.get('name', '未知商品')

        # 检查是否需要拍照
        photo_info = ""
        if self.camera_module and self.camera_module.is_camera_active:
            photo_info = "\n📷 将自动拍照并提交图片"
        elif self.captured_photo_path:
            photo_info = f"\n📷 将提交已拍摄的图片: {os.path.basename(self.captured_photo_path)}"

        reply = QMessageBox.question(
            self,
            "确认提交",
            f"确定要提交以下数据吗？\n\n"
            f"商品: {product_name}\n"
            f"重量: {weights_str} kg\n"
            f"共 {len(self.current_weights)} 个重量值{photo_info}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.perform_weight_submission()

    def perform_weight_submission(self):
        """执行重量提交"""
        try:
            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("正在提交重量数据...")

            # 禁用按钮
            self.submit_btn.setEnabled(False)
            self.add_weight_btn.setEnabled(False)
            self.clear_weights_btn.setEnabled(False)

            # 准备提交数据
            order_detail_id = self.current_product.get('id')
            if not order_detail_id:
                raise ValueError("缺少订单明细ID")

            # 将重量列表转换为逗号分隔的字符串
            quantity_str = ",".join([f"{w:.2f}" for w in self.current_weights])
            stock_mode = self.current_product.get('stock_mode', 0)

            self.progress_bar.setValue(30)

            # 调用API提交
            result = self.api.submit_weight(
                id=order_detail_id,
                quantity=quantity_str,
                stock_mode=stock_mode,
                type=0  # 全新验收
            )

            self.progress_bar.setValue(80)

            # 处理重量提交结果
            if result.get('code') == 200:
                self.progress_bar.setValue(60)
                self.status_label.setText("重量提交成功，准备拍照...")

                # 重量提交成功后，进行拍照和图片提交
                photo_result = self.handle_photo_submission(order_detail_id)

                if photo_result['success']:
                    self.progress_bar.setValue(100)
                    self.status_label.setText("重量和图片提交成功！")

                    success_msg = f"数据已成功提交！\n\n商品: {self.current_product.get('name', '未知商品')}\n重量: {quantity_str} kg"
                    if photo_result.get('photo_info'):
                        success_msg += f"\n图片: {photo_result['photo_info']}"

                    QMessageBox.information(self, "提交成功", success_msg)

                    # 清空重量列表和照片
                    self.clear_weight_list()
                    self.captured_photo_path = None
                    self.current_batch += 1  # 增加批次号
                else:
                    # 重量成功但图片失败
                    self.progress_bar.setValue(80)
                    self.status_label.setText("重量成功，图片提交失败")

                    QMessageBox.warning(
                        self,
                        "部分成功",
                        f"重量数据提交成功，但图片提交失败:\n{photo_result.get('error', '未知错误')}\n\n"
                        f"商品: {self.current_product.get('name', '未知商品')}\n"
                        f"重量: {quantity_str} kg"
                    )

                    # 仍然清空重量列表
                    self.clear_weight_list()
                    self.current_batch += 1

            else:
                error_msg = result.get('msg', '未知错误')
                raise Exception(f"重量提交失败: {error_msg}")

        except Exception as e:
            self.status_label.setText(f"提交失败: {str(e)}")
            QMessageBox.critical(self, "提交失败", f"重量提交失败:\n{str(e)}")

        finally:
            # 恢复界面状态
            self.progress_bar.setVisible(False)
            self.update_button_states()

    def handle_photo_submission(self, order_detail_id: int) -> dict:
        """处理图片提交"""
        try:
            photo_path = None

            # 1. 检查是否有已拍摄的照片
            if self.captured_photo_path and os.path.exists(self.captured_photo_path):
                photo_path = self.captured_photo_path
                self.status_label.setText("使用已拍摄的照片...")

            # 2. 如果没有照片但摄像头可用，自动拍照
            elif self.camera_module and self.camera_module.is_camera_active:
                self.status_label.setText("正在自动拍照...")

                # 生成照片文件名
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"auto_photo_{timestamp}.jpg"
                photo_dir = "photos"
                os.makedirs(photo_dir, exist_ok=True)
                photo_path = os.path.join(photo_dir, filename)

                # 拍照
                if self.camera_module.camera_worker and self.camera_module.camera_worker.capture_photo(photo_path):
                    self.captured_photo_path = photo_path
                    self.status_label.setText(f"自动拍照成功: {filename}")
                else:
                    return {
                        'success': False,
                        'error': '自动拍照失败',
                        'photo_info': None
                    }

            # 3. 如果有照片，提交图片
            if photo_path and os.path.exists(photo_path):
                self.status_label.setText("正在提交图片...")

                # 调用图片提交API
                photo_result = self.api.submit_picture(
                    order_detail_id=order_detail_id,
                    batch=self.current_batch,
                    image_path=photo_path
                )

                if photo_result['success']:
                    return {
                        'success': True,
                        'photo_info': f"{os.path.basename(photo_path)} (批次{self.current_batch})",
                        'server_filename': photo_result['data'].get('filename', '')
                    }
                else:
                    return {
                        'success': False,
                        'error': photo_result['message'],
                        'photo_info': None
                    }

            # 4. 没有照片的情况
            else:
                return {
                    'success': True,  # 没有照片不算失败
                    'photo_info': '未拍摄照片',
                    'error': None
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'图片处理异常: {str(e)}',
                'photo_info': None
            }

    def set_api(self, api):
        """设置API实例"""
        self.api = api

    def on_photo_captured(self, photo_path: str):
        """拍照完成处理"""
        self.captured_photo_path = photo_path
        self.status_label.setText(f"📸 照片已保存: {os.path.basename(photo_path)}")
        print(f"📸 拍照完成: {photo_path}")

    def cleanup(self):
        """清理资源"""
        self.stop_serial_monitoring()
        if hasattr(self, 'camera_module') and self.camera_module:
            self.camera_module.cleanup()

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.cleanup()
        super().closeEvent(event)

    def test_serial_parsing(self):
        """测试串口数据解析（调试用）"""
        test_data = [
            "sg0000.00kg",
            "sg0000.00kg",
            "wg0000.30kg",
            "wg0000.30kg",
            "wg0000.30kg",
            "wg0000.35kg",
            "wg0000.35kg",
            "wg0000.35kg"
        ]

        # print("🧪 测试串口数据解析:")  # 调试输出已关闭
        import re
        pattern = r'^[sw][gn](-?\d*\.?\d+)kg$'

        for data in test_data:
            match = re.match(pattern, data, re.IGNORECASE)
            if match:
                weight_str = match.group(1)
                try:
                    weight_float = float(weight_str)
                    weight = f"{weight_float:.2f}"
                    # print(f"✅ '{data}' -> {weight}kg")  # 调试输出已关闭
                except ValueError:
                    # print(f"❌ 重量数据格式错误: {weight_str}")  # 调试输出已关闭
                    pass
            else:
                # print(f"❌ 串口数据格式不匹配: '{data}'")  # 调试输出已关闭
                pass
