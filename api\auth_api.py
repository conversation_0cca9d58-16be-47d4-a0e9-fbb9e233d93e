#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证API接口
Authentication API
"""

import requests
import json
from typing import Dict, Any, Optional

class AuthAPI:
    """认证API类"""
    
    def __init__(self, base_url: str = "https://[ip:port]/st/steelyard/"):
        """
        初始化认证API
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.access_token = None
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Smart-Canteen-Management-System/1.0.0'
        })
    
    def login(self, username: str, password: str, version: str = "1.8.13") -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            version: 版本号
            
        Returns:
            登录结果字典
        """
        url = f"{self.base_url}/?op=login"
        
        data = {
            'username': username,
            'password': password,
            'version': version
        }
        
        try:
            response = self.session.post(url, data=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            # 如果登录成功，保存access_token
            if result.get('code') == 200 and 'data' in result:
                data_obj = result['data']
                if 'access_token' in data_obj:
                    self.access_token = data_obj['access_token']
                    # 设置后续请求的Authorization头
                    self.session.headers['Authorization'] = self.access_token
            
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                'code': 500,
                'msg': f'网络请求失败: {str(e)}',
                'data': None
            }
        except json.JSONDecodeError as e:
            return {
                'code': 500,
                'msg': f'响应数据解析失败: {str(e)}',
                'data': None
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'未知错误: {str(e)}',
                'data': None
            }
    
    def logout(self) -> Dict[str, Any]:
        """
        用户登出
        
        Returns:
            登出结果字典
        """
        # 清除access_token
        self.access_token = None
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']
        
        return {
            'code': 200,
            'msg': '登出成功',
            'data': None
        }
    
    def is_authenticated(self) -> bool:
        """
        检查是否已认证
        
        Returns:
            是否已认证
        """
        return self.access_token is not None
    
    def get_access_token(self) -> Optional[str]:
        """
        获取访问令牌
        
        Returns:
            访问令牌
        """
        return self.access_token
    
    def set_access_token(self, token: str):
        """
        设置访问令牌
        
        Args:
            token: 访问令牌
        """
        self.access_token = token
        self.session.headers['Authorization'] = token
    
    def verify_token(self) -> Dict[str, Any]:
        """
        验证token是否有效

        Returns:
            验证结果字典
        """
        if not self.access_token:
            return {
                'code': 401,
                'msg': '无token',
                'data': None
            }

        # 尝试调用一个需要认证的接口来验证token
        try:
            # 这里可以调用一个轻量级的验证接口，比如获取用户信息
            # 如果没有专门的验证接口，可以调用任意一个需要认证的接口
            response = self.session.get(f"{self.base_url}/?op=user_info", timeout=5)

            if response.status_code == 401:
                return {
                    'code': 401,
                    'msg': 'token已过期',
                    'data': None
                }
            elif response.status_code == 200:
                return {
                    'code': 200,
                    'msg': 'token有效',
                    'data': response.json()
                }
            else:
                return {
                    'code': response.status_code,
                    'msg': f'验证失败: HTTP {response.status_code}',
                    'data': None
                }

        except requests.exceptions.RequestException as e:
            return {
                'code': 500,
                'msg': f'网络请求失败: {str(e)}',
                'data': None
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'验证过程中发生错误: {str(e)}',
                'data': None
            }

    def make_authenticated_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发起需要认证的请求

        Args:
            method: HTTP方法
            endpoint: 端点
            **kwargs: 其他请求参数

        Returns:
            请求结果字典
        """
        if not self.is_authenticated():
            return {
                'code': 401,
                'msg': '未认证，请先登录',
                'data': None
            }

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            response = self.session.request(method, url, timeout=10, **kwargs)

            # 检查是否是认证失败
            if response.status_code == 401:
                return {
                    'code': 401,
                    'msg': 'token已过期，请重新登录',
                    'data': None
                }

            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            return {
                'code': 500,
                'msg': f'网络请求失败: {str(e)}',
                'data': None
            }
        except json.JSONDecodeError as e:
            return {
                'code': 500,
                'msg': f'响应数据解析失败: {str(e)}',
                'data': None
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'未知错误: {str(e)}',
                'data': None
            }
