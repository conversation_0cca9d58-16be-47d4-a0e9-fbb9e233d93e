#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置文件
System Configuration
"""

import os
import json
from typing import Dict, Any

class Settings:
    """系统设置类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        "api": {
            "base_url": "https://[ip:port]/st/steelyard/",
            "timeout": 10,
            "version": "1.8.13"
        },
        "ui": {
            "window_width": 1200,
            "window_height": 800,
            "fullscreen": True,
            "theme": "glassmorphism"
        },
        "app": {
            "name": "智慧食堂管理系统",
            "version": "1.0.0",
            "organization": "智慧食堂"
        },
        "login": {
            "remember_username": True,
            "auto_login": False
        }
    }
    
    def __init__(self, config_file: str = "config/app_config.json"):
        """
        初始化设置
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self.DEFAULT_CONFIG.copy()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(self.config, user_config)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]):
        """合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def get(self, key: str, default=None):
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    # API相关配置
    @property
    def api_base_url(self) -> str:
        return self.get('api.base_url')
    
    @property
    def api_timeout(self) -> int:
        return self.get('api.timeout')
    
    @property
    def api_version(self) -> str:
        return self.get('api.version')
    
    # UI相关配置
    @property
    def window_width(self) -> int:
        return self.get('ui.window_width')
    
    @property
    def window_height(self) -> int:
        return self.get('ui.window_height')
    
    @property
    def fullscreen(self) -> bool:
        return self.get('ui.fullscreen')
    
    @property
    def theme(self) -> str:
        return self.get('ui.theme')
    
    # 应用相关配置
    @property
    def app_name(self) -> str:
        return self.get('app.name')
    
    @property
    def app_version(self) -> str:
        return self.get('app.version')
    
    @property
    def organization(self) -> str:
        return self.get('app.organization')
    
    # 登录相关配置
    @property
    def remember_username(self) -> bool:
        return self.get('login.remember_username')
    
    @property
    def auto_login(self) -> bool:
        return self.get('login.auto_login')

# 全局设置实例
settings = Settings()
