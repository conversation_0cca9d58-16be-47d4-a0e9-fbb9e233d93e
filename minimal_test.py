#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试
Minimal Test
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("开始测试...")

try:
    print("1. 测试PyQt6导入...")
    from PyQt6.QtWidgets import QApplication, QDialog, QMainWindow
    print("✅ PyQt6导入成功")
    
    print("2. 测试创建应用...")
    app = QApplication(sys.argv)
    print("✅ 应用创建成功")
    
    print("3. 测试导入认证管理器...")
    from utils.auth_manager import AuthManager
    auth_manager = AuthManager()
    print("✅ 认证管理器导入成功")
    
    print("4. 测试导入登录窗口...")
    from ui.login_window import LoginWindow
    print("✅ 登录窗口导入成功")
    
    print("5. 测试创建登录窗口...")
    login_window = LoginWindow()
    print("✅ 登录窗口创建成功")
    
    print("\n🎉 所有测试通过！")
    print("程序应该可以正常运行了")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
